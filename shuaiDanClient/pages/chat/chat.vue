<template>
  <view class="chat-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-left">
        <picker 
          :value="selectedGroupIndex" 
          :range="groupNames" 
          @change="handleGroupChange"
          class="group-picker"
        >
          <view class="picker-content">
            <text class="group-name">{{ currentGroupName }}</text>
            <text class="dropdown-icon">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="header-right">
        <view class="search-icon" @click="handleSearch">🔍</view>
        <view class="avatar" @click="handleProfile">👤</view>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
    >
      <view 
        v-for="message in messages" 
        :key="message.id" 
        class="message-item"
        :class="{ 'system-message': message.message_type === 'system' }"
      >
        <!-- 系统消息 -->
        <view v-if="message.message_type === 'system'" class="system-content">
          <text class="system-text">{{ message.content }}</text>
        </view>
        
        <!-- 普通消息 -->
        <view v-else class="normal-message">
          <view class="message-header">
            <text class="sender-name">{{ message.sender.nickname }}</text>
            <text class="message-time">{{ formatTime(message.created_at) }}</text>
          </view>
          <view class="message-content">
            <text>{{ message.content }}</text>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="loading-more">
        <text>加载中...</text>
      </view>
    </scroll-view>

    <!-- 功能按钮区域 -->
    <view class="function-buttons">
      <button class="func-btn" style="background-color: #8b5cf6;" @click="handleCompanyTransfer">公司转让</button>
      <button class="func-btn" style="background-color: #06b6d4;" @click="handleLocalPeers">本地同行</button>
      <button class="func-btn" style="background-color: #22c55e;" @click="handleListingTrade">挂牌交易</button>
      <button class="func-btn" style="background-color: #f59e0b;" @click="handleYellowLight">黄光</button>
      <button class="func-btn" style="background-color: #ef4444;" @click="handleGroupAnnouncement">群公告</button>
    </view>

    <!-- 底部输入区域 -->
    <view class="input-section">
      <view class="input-container">
        <input 
          v-model="inputMessage"
          class="message-input" 
          placeholder="请输入您的接单信息"
          @confirm="sendMessage"
          confirm-type="send"
        />
        <button class="send-btn" @click="sendMessage" :disabled="!inputMessage.trim()">发送</button>
      </view>
      <button class="private-msg-btn" @click="handlePrivateMessage">私信</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { chatAPI, utils } from '@/utils/api.js'

// 响应式数据
// 群组相关
const groups = ref([])
const selectedGroupIndex = ref(0)
const currentGroupId = ref(1) // 默认群组ID

// 消息相关
const messages = ref([])
const inputMessage = ref('')
const lastMessageId = ref(0)
const isLoadingMore = ref(false)
const scrollTop = ref(0)

// 轮询相关
const pollingTimer = ref(null)
const pollingInterval = ref(3000) // 3秒轮询一次

// 用户信息
const userInfo = ref(null)
const isLoggedIn = ref(false)

// 计算属性
const groupNames = computed(() => {
  return groups.value.map(group => group.group_name || '未知群组')
})

const currentGroupName = computed(() => {
  return groups.value[selectedGroupIndex.value]?.group_name || '全国接单总群'
})

// 生命周期钩子
onMounted(() => {
  initPage()
})

onUnmounted(() => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }
})

// 方法定义
/**
 * 初始化页面
 */
const initPage = async () => {
  // 检查登录状态
  checkLoginStatus()

  // 加载群组列表
  await loadGroups()

  // 加载消息
  await loadMessages()

  // 开始轮询
  startPolling()
}

/**
 * 检查登录状态
 */
const checkLoginStatus = () => {
  const token = uni.getStorageSync('token')
  const userInfoData = uni.getStorageSync('userInfo')

  isLoggedIn.value = !!token
  userInfo.value = userInfoData
}

/**
 * 加载群组列表
 */
const loadGroups = async () => {
  try {
    if (isLoggedIn.value) {
      // 已登录用户获取真实群组数据
      const result = await chatAPI.getMyGroups()
      if (result.success) {
        groups.value = result.data.map(item => ({
          id: item.group_id,
          group_name: item.group.group_name
        }))
      }
    }

    // 如果没有群组或未登录，使用默认群组
    if (groups.value.length === 0) {
      groups.value = [
        { id: 1, group_name: '全国接单总群' },
        { id: 2, group_name: '北京同行群' },
        { id: 3, group_name: '上海同行群' },
        { id: 4, group_name: '广州同行群' },
        { id: 5, group_name: '深圳同行群' }
      ]
    }

    currentGroupId.value = groups.value[0]?.id || 1
  } catch (error) {
    console.error('加载群组失败:', error)
    // 出错时使用默认群组
    groups.value = [
      { id: 1, group_name: '全国接单总群' }
    ]
    currentGroupId.value = 1
  }
}

/**
 * 加载消息列表
 */
const loadMessages = async () => {
  try {
    if (isLoggedIn.value && currentGroupId.value) {
      // 已登录用户获取真实消息数据
      const result = await chatAPI.getGroupMessages(currentGroupId.value, 1, 50)
      if (result.success && result.data.data) {
        messages.value = result.data.data.reverse() // 反转数组，最新消息在底部
        if (messages.value.length > 0) {
          lastMessageId.value = Math.max(...messages.value.map(m => m.id))
        }
      }
    } else {
      // 未登录或无群组时显示模拟数据
      const mockMessages = [
        {
          id: 1,
          message_type: 'system',
          content: '15123054535 邀请 用户678115 加入群聊',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          message_type: 'system',
          content: '用户086933 邀请 用户621269 加入群聊',
          created_at: new Date().toISOString()
        },
        {
          id: 3,
          message_type: 'text',
          content: '客户需求：买一个个体\n要求：1年以上 最好为重庆市江北区\n他们做卖茶叶的 最好是跟这个相关的',
          sender: { nickname: '重庆群-53分钟前' },
          created_at: new Date().toISOString()
        },
        {
          id: 4,
          message_type: 'text',
          content: '收一家带三类医疗的科技公司，高新区最好',
          sender: { nickname: '四川群-73分钟前' },
          created_at: new Date().toISOString()
        }
      ]

      messages.value = mockMessages
      lastMessageId.value = Math.max(...mockMessages.map(m => m.id))
    }

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('加载消息失败:', error)
    utils.handleError(error, '加载消息失败')
  }
}

/**
 * 发送消息
 */
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  const messageContent = inputMessage.value.trim()
  inputMessage.value = ''

  try {
    // 调用API发送消息
    const result = await chatAPI.sendMessage(currentGroupId.value, messageContent)

    if (result.success) {
      // 添加到消息列表
      messages.value.push(result.data)
      lastMessageId.value = result.data.id

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })

      uni.showToast({
        title: '发送成功',
        icon: 'success'
      })
    } else {
      throw new Error(result.message || '发送失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    utils.handleError(error, '发送消息失败')

    // 恢复输入内容
    inputMessage.value = messageContent
  }
}

/**
 * 开始轮询新消息
 */
const startPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  pollingTimer.value = setInterval(() => {
    pollNewMessages()
  }, pollingInterval.value)
}

/**
 * 轮询新消息
 */
const pollNewMessages = async () => {
  try {
    if (!isLoggedIn.value || !currentGroupId.value) {
      return
    }

    // 调用API获取新消息
    const result = await chatAPI.getNewMessages(currentGroupId.value, lastMessageId.value)

    if (result.success && result.data.length > 0) {
      // 添加新消息到列表
      messages.value.push(...result.data)

      // 更新最后消息ID
      const newLastId = Math.max(...result.data.map(m => m.id))
      if (newLastId > lastMessageId.value) {
        lastMessageId.value = newLastId
      }

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    }
  } catch (error) {
    console.error('轮询新消息失败:', error)
    // 轮询失败不显示错误提示，避免频繁弹窗
  }
}

/**
 * 滚动到底部
 */
const scrollToBottom = () => {
  scrollTop.value = 999999
}

/**
 * 格式化时间
 */
const formatTime = (timeStr) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now - time

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) { // 24小时内
    return Math.floor(diff / 3600000) + '小时前'
  } else {
    return time.toLocaleDateString()
  }
}

// 事件处理方法
const handleGroupChange = (e) => {
  selectedGroupIndex.value = e.detail.value
  currentGroupId.value = groups.value[selectedGroupIndex.value]?.id
  loadMessages() // 重新加载消息
}

const handleSearch = () => {
  uni.showToast({ title: '搜索功能开发中', icon: 'none' })
}

const handleProfile = () => {
  if (isLoggedIn.value) {
    uni.showToast({ title: '个人中心开发中', icon: 'none' })
  } else {
    uni.navigateTo({ url: '/pages/login/login' })
  }
}

const handleCompanyTransfer = () => {
  uni.showToast({ title: '公司转让功能开发中', icon: 'none' })
}

const handleLocalPeers = () => {
  uni.showToast({ title: '本地同行功能开发中', icon: 'none' })
}

const handleListingTrade = () => {
  uni.showToast({ title: '挂牌交易功能开发中', icon: 'none' })
}

const handleYellowLight = () => {
  uni.showToast({ title: '黄光功能开发中', icon: 'none' })
}

const handleGroupAnnouncement = () => {
  uni.showToast({ title: '群公告功能开发中', icon: 'none' })
}

const handlePrivateMessage = () => {
  uni.showToast({ title: '私信功能开发中', icon: 'none' })
}

const loadMoreMessages = () => {
  // 加载更多历史消息
  uni.showToast({ title: '加载更多功能开发中', icon: 'none' })
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  background-color: white;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e5e5e5;
}

.group-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  align-items: center;
}

.group-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.dropdown-icon {
  font-size: 24rpx;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.search-icon, .avatar {
  font-size: 36rpx;
  padding: 10rpx;
}

.message-list {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  margin-bottom: 20rpx;
}

.system-message {
  text-align: center;
}

.system-content {
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.system-text {
  font-size: 24rpx;
  color: #666;
}

.normal-message {
  background-color: white;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.sender-name {
  font-size: 28rpx;
  color: #22c55e;
  font-weight: bold;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.function-buttons {
  display: flex;
  padding: 20rpx;
  gap: 10rpx;
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
}

.func-btn {
  flex: 1;
  height: 60rpx;
  font-size: 24rpx;
  color: white;
  border: none;
  border-radius: 8rpx;
}

.input-section {
  background-color: white;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
}

.message-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.send-btn {
  background-color: #22c55e;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.send-btn:disabled {
  background-color: #ccc;
}

.private-msg-btn {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

.loading-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}
</style>
