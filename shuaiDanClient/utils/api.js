/**
 * API工具类
 * 统一管理所有API请求
 */

// API基础配置
const API_CONFIG = {
  baseURL: 'http://localhost:3000/api/v1',
  timeout: 10000
}

/**
 * 通用请求方法
 * @param {Object} options 请求配置
 * @returns {Promise}
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync('token')
    
    // 构建请求配置
    const config = {
      url: API_CONFIG.baseURL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || API_CONFIG.timeout
    }
    
    // 添加认证头
    if (token) {
      config.header.Authorization = `Bearer ${token}`
    }
    
    // 发送请求
    uni.request({
      ...config,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))
        }
      },
      fail: (err) => {
        console.error('请求失败:', err)
        reject(new Error('网络请求失败'))
      }
    })
  })
}

/**
 * 认证相关API
 */
export const authAPI = {
  /**
   * 微信登录
   * @param {string} code 微信登录code
   * @param {number} inviterId 邀请者ID（可选）
   */
  login(code, inviterId) {
    return request({
      url: '/auth/login',
      method: 'POST',
      data: { code, inviterId }
    })
  },

  /**
   * 刷新token
   * @param {string} token 旧token
   */
  refreshToken(token) {
    return request({
      url: '/auth/refresh',
      method: 'POST',
      data: { token }
    })
  },

  /**
   * 登出
   */
  logout() {
    return request({
      url: '/auth/logout',
      method: 'POST'
    })
  }
}

/**
 * 用户相关API
 */
export const userAPI = {
  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request({
      url: '/users/me'
    })
  },

  /**
   * 更新用户信息
   * @param {Object} userData 用户数据
   */
  updateUser(userData) {
    return request({
      url: '/users/me',
      method: 'PUT',
      data: userData
    })
  }
}

/**
 * 聊天相关API
 */
export const chatAPI = {
  /**
   * 获取群组列表
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   */
  getGroups(page = 1, pageSize = 20) {
    return request({
      url: `/chat/groups?page=${page}&pageSize=${pageSize}`
    })
  },

  /**
   * 获取我的群组
   */
  getMyGroups() {
    return request({
      url: '/chat/my-groups'
    })
  },

  /**
   * 获取群组详情
   * @param {number} groupId 群组ID
   */
  getGroupDetail(groupId) {
    return request({
      url: `/chat/groups/${groupId}`
    })
  },

  /**
   * 加入群组
   * @param {number} groupId 群组ID
   */
  joinGroup(groupId) {
    return request({
      url: `/chat/groups/${groupId}/join`,
      method: 'POST'
    })
  },

  /**
   * 退出群组
   * @param {number} groupId 群组ID
   */
  leaveGroup(groupId) {
    return request({
      url: `/chat/groups/${groupId}/leave`,
      method: 'POST'
    })
  },

  /**
   * 获取群组成员
   * @param {number} groupId 群组ID
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   */
  getGroupMembers(groupId, page = 1, pageSize = 50) {
    return request({
      url: `/chat/groups/${groupId}/members?page=${page}&pageSize=${pageSize}`
    })
  },

  /**
   * 发送消息
   * @param {number} groupId 群组ID
   * @param {string} content 消息内容
   * @param {string} messageType 消息类型
   */
  sendMessage(groupId, content, messageType = 'text') {
    return request({
      url: `/chat/groups/${groupId}/messages`,
      method: 'POST',
      data: {
        content,
        message_type: messageType
      }
    })
  },

  /**
   * 获取群组消息
   * @param {number} groupId 群组ID
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   * @param {number} lastMessageId 最后一条消息ID
   */
  getGroupMessages(groupId, page = 1, pageSize = 50, lastMessageId) {
    let url = `/chat/groups/${groupId}/messages?page=${page}&pageSize=${pageSize}`
    if (lastMessageId) {
      url += `&lastMessageId=${lastMessageId}`
    }
    return request({ url })
  },

  /**
   * 获取新消息（轮询用）
   * @param {number} groupId 群组ID
   * @param {number} lastMessageId 最后一条消息ID
   */
  getNewMessages(groupId, lastMessageId = 0) {
    return request({
      url: `/chat/groups/${groupId}/new-messages?lastMessageId=${lastMessageId}`
    })
  }
}

/**
 * 挂牌信息相关API
 */
export const listingAPI = {
  /**
   * 获取挂牌信息列表
   * @param {Object} params 查询参数
   */
  getListings(params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    return request({
      url: `/listings${queryString ? '?' + queryString : ''}`
    })
  },

  /**
   * 获取挂牌信息详情
   * @param {number} id 信息ID
   */
  getListingDetail(id) {
    return request({
      url: `/listings/${id}`
    })
  },

  /**
   * 发布挂牌信息
   * @param {Object} listingData 挂牌信息数据
   */
  createListing(listingData) {
    return request({
      url: '/listings',
      method: 'POST',
      data: listingData
    })
  }
}

/**
 * 工具方法
 */
export const utils = {
  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @param {string} defaultMessage 默认错误消息
   */
  handleError(error, defaultMessage = '操作失败') {
    console.error('API错误:', error)
    
    let message = defaultMessage
    if (error.message) {
      message = error.message
    }
    
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 检查登录状态
   * @returns {boolean}
   */
  isLoggedIn() {
    return !!uni.getStorageSync('token')
  },

  /**
   * 清除登录信息
   */
  clearAuth() {
    uni.removeStorageSync('token')
    uni.removeStorageSync('userInfo')
  }
}
