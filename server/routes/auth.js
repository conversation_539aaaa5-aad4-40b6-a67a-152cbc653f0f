const express = require('express');
const router = express.Router();

const User = require('../models/User');
const wechatAPI = require('../utils/wechat');
const { generateToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');

/**
 * 接口 1: 微信静默登录
 * POST /api/v1/auth/login
 * 
 * 输入: 小程序端通过 wx.login() 获取的 code
 * 逻辑: 调用微信官方 code2Session 接口，用 code 换取用户的 openid 和 session_key
 * 数据库: 根据 openid 查询 users 表。若用户不存在，则创建新用户；若存在，则更新最后登录时间
 * 输出: 生成 JWT，将 user_id, openid 等信息加密后返回给小程序端
 */
router.post('/login', async (req, res) => {
  try {
    const { code, inviterId } = req.body;

    // 验证必填参数
    if (!code) {
      return ResponseHelper.validationError(res, ['code 是必填参数']);
    }

    // 调用微信接口获取用户信息
    let wechatData;
    try {
      wechatData = await wechatAPI.code2Session(code);
    } catch (error) {
      console.error('微信登录失败:', error);
      return ResponseHelper.error(res, '微信登录失败，请重试', 400);
    }

    const { openid, session_key } = wechatData;

    // 查找或创建用户
    let user = await User.findByOpenid(openid);
    
    if (user) {
      // 用户已存在，更新最后登录时间
      await User.updateLastLogin(user.id);
    } else {
      // 新用户，创建账户
      const userData = {
        openid,
        status: 'inactive',
        publishing_credits: 0
      };

      // 如果有邀请者ID，设置邀请关系
      if (inviterId) {
        const inviter = await User.findById(inviterId);
        if (inviter) {
          userData.inviter_id = inviterId;
          // 这里可以添加邀请奖励逻辑
        }
      }

      user = await User.create(userData);
    }

    // 生成JWT token
    const token = generateToken({
      user_id: user.id,
      openid: user.openid
    });

    // 返回登录成功信息
    ResponseHelper.success(res, {
      token,
      user: user.toJSON(),
      isNewUser: !user.created_at || new Date() - new Date(user.created_at) < 60000 // 1分钟内创建的算新用户
    }, '登录成功');

  } catch (error) {
    console.error('登录接口错误:', error);
    ResponseHelper.serverError(res, '登录失败', error);
  }
});

/**
 * 刷新token接口
 * POST /api/v1/auth/refresh
 */
router.post('/refresh', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return ResponseHelper.validationError(res, ['token 是必填参数']);
    }

    // 验证旧token（即使过期也要能解析）
    const jwt = require('jsonwebtoken');
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        // token过期，尝试解析
        decoded = jwt.decode(token);
      } else {
        return ResponseHelper.unauthorized(res, '无效的token');
      }
    }

    if (!decoded || !decoded.user_id) {
      return ResponseHelper.unauthorized(res, '无效的token');
    }

    // 查找用户
    const user = await User.findById(decoded.user_id);
    if (!user) {
      return ResponseHelper.unauthorized(res, '用户不存在');
    }

    // 生成新token
    const newToken = generateToken({
      user_id: user.id,
      openid: user.openid
    });

    ResponseHelper.success(res, {
      token: newToken,
      user: user.toJSON()
    }, 'Token刷新成功');

  } catch (error) {
    console.error('刷新token错误:', error);
    ResponseHelper.serverError(res, 'Token刷新失败', error);
  }
});

/**
 * 登出接口
 * POST /api/v1/auth/logout
 */
router.post('/logout', (req, res) => {
  // JWT是无状态的，客户端删除token即可
  ResponseHelper.success(res, null, '登出成功');
});

module.exports = router;
