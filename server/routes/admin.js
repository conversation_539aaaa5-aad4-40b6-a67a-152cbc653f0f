const express = require('express');
const router = express.Router();
const Administrator = require('../models/Administrator');
const User = require('../models/User');
const Listing = require('../models/Listing');
const { authenticateAdmin, generateAdminToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');
const Validator = require('../utils/validator');

/**
 * 管理员登录
 * POST /api/v1/admin/login
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return ResponseHelper.validationError(res, ['用户名和密码不能为空']);
    }

    // 查找管理员
    const admin = await Administrator.findByUsername(username);
    if (!admin) {
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 验证密码
    const isValidPassword = await admin.verifyPassword(password);
    if (!isValidPassword) {
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 生成token
    const token = generateAdminToken(admin);

    ResponseHelper.success(res, {
      admin: admin.toJSON(),
      token
    }, '登录成功');

  } catch (error) {
    console.error('管理员登录错误:', error);
    ResponseHelper.serverError(res, '登录失败', error);
  }
});

/**
 * 获取管理员信息
 * GET /api/v1/admin/me
 */
router.get('/me', authenticateAdmin, async (req, res) => {
  try {
    const admin = await Administrator.findById(req.admin.id);
    if (!admin) {
      return ResponseHelper.notFound(res, '管理员不存在');
    }

    ResponseHelper.success(res, admin.toJSON(), '获取管理员信息成功');

  } catch (error) {
    console.error('获取管理员信息错误:', error);
    ResponseHelper.serverError(res, '获取管理员信息失败', error);
  }
});

/**
 * 获取仪表盘统计数据
 * GET /api/v1/admin/dashboard/stats
 */
router.get('/dashboard/stats', authenticateAdmin, async (req, res) => {
  try {
    // 获取用户统计
    const userStats = await User.getStats();

    // 获取挂牌信息统计
    const listingStats = await Listing.getStats();

    const stats = {
      users: userStats,
      listings: listingStats,
      orders: {
        total: 0,
        today: 0
      },
      revenue: {
        total: 0,
        today: 0
      }
    };

    ResponseHelper.success(res, stats, '获取统计数据成功');

  } catch (error) {
    console.error('获取统计数据错误:', error);
    ResponseHelper.serverError(res, '获取统计数据失败', error);
  }
});

/**
 * 创建管理员
 * POST /api/v1/admin/administrators
 */
router.post('/administrators', authenticateAdmin, async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return ResponseHelper.validationError(res, ['用户名和密码不能为空']);
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 50) {
      return ResponseHelper.validationError(res, ['用户名长度必须在3-50个字符之间']);
    }

    // 验证密码长度
    if (password.length < 6) {
      return ResponseHelper.validationError(res, ['密码长度不能少于6个字符']);
    }

    // 检查用户名是否已存在
    const existingAdmin = await Administrator.findByUsername(username);
    if (existingAdmin) {
      return ResponseHelper.validationError(res, ['用户名已存在']);
    }

    // 创建管理员
    const newAdmin = await Administrator.create({ username, password });

    ResponseHelper.success(res, newAdmin.toJSON(), '创建管理员成功');

  } catch (error) {
    console.error('创建管理员错误:', error);
    ResponseHelper.serverError(res, '创建管理员失败', error);
  }
});

/**
 * 获取管理员列表
 * GET /api/v1/admin/administrators
 */
router.get('/administrators', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    const result = await Administrator.getList({ page, pageSize });
    
    ResponseHelper.paginated(res, result.data.map(admin => admin.toJSON()), result.pagination, '获取管理员列表成功');

  } catch (error) {
    console.error('获取管理员列表错误:', error);
    ResponseHelper.serverError(res, '获取管理员列表失败', error);
  }
});

/**
 * 删除管理员
 * DELETE /api/v1/admin/administrators/:id
 */
router.delete('/administrators/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const currentAdminId = req.admin.id;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的管理员ID']);
    }

    const adminId = parseInt(id);

    // 不能删除自己
    if (adminId === currentAdminId) {
      return ResponseHelper.forbidden(res, '不能删除自己的账号');
    }

    // 检查管理员是否存在
    const admin = await Administrator.findById(adminId);
    if (!admin) {
      return ResponseHelper.notFound(res, '管理员不存在');
    }

    // 删除管理员
    const deleted = await Administrator.delete(adminId);
    if (!deleted) {
      return ResponseHelper.serverError(res, '删除失败');
    }

    ResponseHelper.success(res, null, '删除管理员成功');

  } catch (error) {
    console.error('删除管理员错误:', error);
    ResponseHelper.serverError(res, '删除管理员失败', error);
  }
});

/**
 * 获取用户列表（管理员功能）
 * GET /api/v1/admin/users
 */
router.get('/users', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, search } = req.query;

    const result = await User.getList({ page, pageSize, status, search });

    ResponseHelper.paginated(res, result.data.map(user => user.toJSON()), result.pagination, '获取用户列表成功');

  } catch (error) {
    console.error('获取用户列表错误:', error);
    ResponseHelper.serverError(res, '获取用户列表失败', error);
  }
});

/**
 * 获取用户详情（管理员功能）
 * GET /api/v1/admin/users/:id
 */
router.get('/users/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    const userId = parseInt(id);

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    ResponseHelper.success(res, user.toJSON(), '获取用户详情成功');

  } catch (error) {
    console.error('获取用户详情错误:', error);
    ResponseHelper.serverError(res, '获取用户详情失败', error);
  }
});

/**
 * 激活/停用用户
 * PUT /api/v1/admin/users/:id/status
 */
router.put('/users/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    // 验证状态值
    if (!status || !['active', 'inactive'].includes(status)) {
      return ResponseHelper.validationError(res, ['状态值必须是 active 或 inactive']);
    }

    const userId = parseInt(id);

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    // 更新用户状态
    const updatedUser = await User.update(userId, { status });

    ResponseHelper.success(res, updatedUser.toJSON(), '用户状态更新成功');

  } catch (error) {
    console.error('更新用户状态错误:', error);
    ResponseHelper.serverError(res, '更新用户状态失败', error);
  }
});

/**
 * 修改用户发布额度
 * PUT /api/v1/admin/users/:id/credits
 */
router.put('/users/:id/credits', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { credits } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    // 验证额度值
    if (!credits || isNaN(parseInt(credits)) || parseInt(credits) <= 0) {
      return ResponseHelper.validationError(res, ['额度必须是大于0的数字']);
    }

    const userId = parseInt(id);
    const creditsToAdd = parseInt(credits);

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    // 增加用户额度
    const newCredits = user.publishing_credits + creditsToAdd;
    const updatedUser = await User.update(userId, { publishing_credits: newCredits });

    ResponseHelper.success(res, updatedUser.toJSON(), '用户额度修改成功');

  } catch (error) {
    console.error('修改用户额度错误:', error);
    ResponseHelper.serverError(res, '修改用户额度失败', error);
  }
});

/**
 * 获取信息列表（管理员功能）
 * GET /api/v1/admin/listings
 */
router.get('/listings', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, listing_type, search } = req.query;

    const result = await Listing.getList({ page, pageSize, status, listing_type, search });

    ResponseHelper.paginated(res, result.data.map(listing => listing.toJSON()), result.pagination, '获取信息列表成功');

  } catch (error) {
    console.error('获取信息列表错误:', error);
    ResponseHelper.serverError(res, '获取信息列表失败', error);
  }
});

/**
 * 获取信息详情（管理员功能）
 * GET /api/v1/admin/listings/:id
 */
router.get('/listings/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await Listing.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    ResponseHelper.success(res, listing.toJSON(), '获取信息详情成功');

  } catch (error) {
    console.error('获取信息详情错误:', error);
    ResponseHelper.serverError(res, '获取信息详情失败', error);
  }
});

/**
 * 更新信息状态（管理员功能）
 * PUT /api/v1/admin/listings/:id/status
 */
router.put('/listings/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    // 验证状态值
    if (!status || !['在售', '已售', '下架'].includes(status)) {
      return ResponseHelper.validationError(res, ['状态值必须是 在售、已售 或 下架']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await Listing.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 更新信息状态
    const updatedListing = await Listing.update(listingId, { status });

    ResponseHelper.success(res, updatedListing.toJSON(), '信息状态更新成功');

  } catch (error) {
    console.error('更新信息状态错误:', error);
    ResponseHelper.serverError(res, '更新信息状态失败', error);
  }
});

/**
 * 删除信息（管理员功能）
 * DELETE /api/v1/admin/listings/:id
 */
router.delete('/listings/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await Listing.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 删除信息
    const deleted = await Listing.delete(listingId);
    if (!deleted) {
      return ResponseHelper.serverError(res, '删除失败');
    }

    ResponseHelper.success(res, null, '删除信息成功');

  } catch (error) {
    console.error('删除信息错误:', error);
    ResponseHelper.serverError(res, '删除信息失败', error);
  }
});

module.exports = router;
